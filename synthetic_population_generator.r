# =============================================================================
# EVIDENCE-BASED SYNTHETIC PATIENT POPULATION GENERATOR FOR IBRUTINIB SAFETY TRIAL
# =============================================================================
# Updated with comprehensive evidence from meta-analyses (14,000+ patients)
# Incorporates 2024 bleeding risk data, population demographics, and clinical patterns

# Load file utilities first
if (file.exists("ml_file_utils.r")) {
  source("ml_file_utils.r")
} else {
  cat("Warning: ml_file_utils.r not found. Using basic file operations.\n")
}

library(dplyr)
library(ggplot2)
library(MASS)  # For multivariate normal distribution
library(truncnorm)  # For truncated normal distributions
set.seed(42)  # For reproducible results

# =============================================================================
# PART 1: DEMOGRAPHIC VARIABLES GENERATOR
# =============================================================================

generate_demographics <- function(n_patients = 5000) {
  
  # UPDATED: CLL age distribution (EVIdeNCE study, N=309)
  # 23.0% under 65, 16.8% aged 65-69, 60.2% aged 70 or older
  # Median age 70-72 years across multiple international cohorts
  age_groups <- sample(c("under_65", "65_69", "70_plus"), n_patients, 
                      replace = TRUE, prob = c(0.23, 0.168, 0.602))
  
  age <- numeric(n_patients)
  age[age_groups == "under_65"] <- round(rtruncnorm(sum(age_groups == "under_65"), 
                                                   a = 40, b = 64, mean = 58, sd = 8))
  age[age_groups == "65_69"] <- round(rtruncnorm(sum(age_groups == "65_69"), 
                                                a = 65, b = 69, mean = 67, sd = 1.5))
  age[age_groups == "70_plus"] <- round(rtruncnorm(sum(age_groups == "70_plus"), 
                                                  a = 70, b = 90, mean = 75, sd = 6))

  # UPDATED: CLL sex distribution (slight male predominance in clinical practice)
  sex <- sample(c("Male", "Female"), n_patients, replace = TRUE, prob = c(0.58, 0.42))
  
  # Weight distribution by sex (kg) - adjusted for elderly population
  weight <- ifelse(sex == "Male",
                   rtruncnorm(n_patients, a = 55, b = 120, mean = 78, sd = 14),
                   rtruncnorm(n_patients, a = 45, b = 110, mean = 68, sd = 13))
  weight <- round(weight, 1)
  
  # Height distribution by sex (cm) - adjusted for elderly cohort
  height <- ifelse(sex == "Male",
                   rtruncnorm(n_patients, a = 160, b = 185, mean = 173, sd = 7),
                   rtruncnorm(n_patients, a = 150, b = 175, mean = 160, sd = 6))
  height <- round(height, 1)
  
  # BMI calculation
  bmi <- round(weight / (height/100)^2, 1)
  
  # UPDATED: Ethnicity distribution for CLL populations
  # Based on major international studies
  ethnicity <- sample(c("Caucasian", "Asian", "African American", "Hispanic", "Other"),
                      n_patients, replace = TRUE,
                      prob = c(0.85, 0.08, 0.04, 0.025, 0.005))  # CLL predominantly Caucasian
  
  # Body Surface Area (BSA) using Mosteller formula
  bsa <- round(sqrt((height * weight) / 3600), 2)
  
  # UPDATED: Age-adjusted creatinine clearance (elderly CLL population)
  creatinine_base <- ifelse(sex == "Male", 1.2, 1.0)  # Higher baseline for elderly
  creatinine <- rtruncnorm(n_patients, a = 0.8, b = 4.0, 
                          mean = creatinine_base + (age - 65) * 0.01, sd = 0.4)
  
  # Cockroft-Gault with age adjustment
  creatinine_clearance <- ifelse(sex == "Male",
                                ((140 - age) * weight) / (72 * creatinine),
                                ((140 - age) * weight * 0.85) / (72 * creatinine))
  creatinine_clearance <- round(pmax(creatinine_clearance, 15), 1)  # CLL patients can have lower GFR
  
  return(data.frame(
    patient_id = paste0("PT", sprintf("%04d", 1:n_patients)),
    age = age,
    age_group = age_groups,
    sex = sex,
    weight = weight,
    height = height,
    bmi = bmi,
    ethnicity = ethnicity,
    bsa = bsa,
    creatinine = round(creatinine, 2),
    creatinine_clearance = creatinine_clearance
  ))
}

# =============================================================================
# PART 2: CLINICAL VARIABLES GENERATOR
# =============================================================================

generate_clinical_variables <- function(demographics) {
  n_patients <- nrow(demographics)
  age <- demographics$age
  
  # UPDATED: CLL-specific baseline platelet count distribution
  # Reflects disease-related thrombocytopenia risk
  platelet_base <- 220 - (age - 40) * 2.0  # More pronounced age effect in CLL
  platelet_count <- rtruncnorm(n_patients, a = 75, b = 400,
                               mean = platelet_base, sd = 60)
  platelet_count <- round(platelet_count)
  
  # UPDATED: Comorbidity prevalence (EVIdeNCE study)
  # Cardiovascular disease: 33.3% with history
  cvd_prob <- pmin(0.10 + (age - 40) * 0.018, 0.65)
  cardiovascular_disease <- rbinom(n_patients, 1, cvd_prob)
  
  # Among those with CVD history, 76.7% have ongoing disorders
  ongoing_cvd <- ifelse(cardiovascular_disease, rbinom(n_patients, 1, 0.767), 0)
  
  # CIRS score ≥6 (significant comorbidity burden): 28.1%
  high_comorbidity_score <- rbinom(n_patients, 1, 0.281)
  
  # Diabetes: 8.4% (lower than general population)
  diabetes_prob <- pmin(0.02 + (age - 40) * 0.004 + pmax(demographics$bmi - 25, 0) * 0.008, 0.15)
  diabetes <- rbinom(n_patients, 1, diabetes_prob)
  
  # UPDATED: Age-dependent hypertension (higher in elderly CLL)
  hypertension_prob <- pmin(0.15 + (age - 40) * 0.02, 0.85)
  hypertension <- rbinom(n_patients, 1, hypertension_prob)
  
  # Atrial fibrillation (important for anticoagulation risk)
  # Higher prevalence due to ibrutinib cardiotoxicity
  afib_prob <- pmin(0.02 + (age - 40) * 0.012 + cardiovascular_disease * 0.15, 0.4)
  atrial_fibrillation <- rbinom(n_patients, 1, afib_prob)
  
  # UPDATED: CLL-specific liver function (may be affected by disease/prior therapy)
  alt <- rtruncnorm(n_patients, a = 15, b = 150, mean = 35, sd = 20)
  ast <- rtruncnorm(n_patients, a = 15, b = 150, mean = 38, sd = 22)
  bilirubin <- rtruncnorm(n_patients, a = 0.3, b = 3.5, mean = 1.0, sd = 0.5)
  
  # UPDATED: Concomitant medication patterns
  # Anticoagulants: 11-23% prevalence (major bleeding risk factor)
  # OR 2.54 for bleeding risk
  anticoagulant_prob <- pmax(0.11, 0.15 * cardiovascular_disease + 0.65 * atrial_fibrillation)
  anticoagulants <- rbinom(n_patients, 1, anticoagulant_prob)
  
  # Anticoagulant type distribution (among users)
  # 60.69% DOACs, 19.4-21.2% warfarin, remainder other
  anticoag_type <- character(n_patients)
  anticoag_type[anticoagulants == 1] <- sample(c("DOAC", "Warfarin", "Other"), 
                                               sum(anticoagulants), replace = TRUE,
                                               prob = c(0.607, 0.205, 0.188))
  anticoag_type[anticoagulants == 0] <- "None"
  
  # Among DOACs: Apixaban 76.7%, others 23.3%
  doac_type <- character(n_patients)
  doac_type[anticoag_type == "DOAC"] <- sample(c("Apixaban", "Rivaroxaban", "Other_DOAC"),
                                               sum(anticoag_type == "DOAC"), replace = TRUE,
                                               prob = c(0.767, 0.15, 0.083))
  doac_type[anticoag_type != "DOAC"] <- "None"
  
  # Antiplatelet agents: 34% prevalence
  antiplatelet_prob <- pmax(0.15, 0.5 * cardiovascular_disease + 0.3 * diabetes)
  antiplatelets <- rbinom(n_patients, 1, antiplatelet_prob)
  
  # UPDATED: CYP3A4 inhibitors/inducers (clinical prevalence)
  cyp3a4_inhibitors <- rbinom(n_patients, 1, 0.18)  # Higher in elderly
  cyp3a4_inducers <- rbinom(n_patients, 1, 0.06)   # Lower in elderly
  
  # Proton pump inhibitors: 13% prevalence (affects absorption)
  ppi_use <- rbinom(n_patients, 1, 0.13)
  
  # Antihypertensive medications: 35%
  antihypertensives <- rbinom(n_patients, 1, 0.35)
  
  # Uric acid inhibitors: 34%
  uric_acid_inhibitors <- rbinom(n_patients, 1, 0.34)
  
  # UPDATED: CLL disease characteristics
  # High-risk genomic features
  del17p_tp53 <- rbinom(n_patients, 1, 0.509)  # 41.8-60.9% range, use middle
  unmutated_ighv <- rbinom(n_patients, 1, 0.741)  # 67.2-81.0% range
  
  # CLL stage distribution (real-world skews toward advanced)
  cll_stage <- sample(c("0", "I", "II", "III", "IV"),
                     n_patients, replace = TRUE,
                     prob = c(0.05, 0.15, 0.25, 0.35, 0.20))  # More advanced than previous
  
  # Prior treatments (real-world patients often heavily pretreated)
  prior_treatments <- rpois(n_patients, lambda = 1.2)  # Increased from 0.5
  
  # UPDATED: Baseline platelet dysfunction markers
  # Critical for bleeding risk assessment
  # von Willebrand factor activity ≤100 IU/dL: HR 2.73
  vwf_activity <- rtruncnorm(n_patients, a = 40, b = 200, mean = 95, sd = 25)
  vwf_low <- as.numeric(vwf_activity <= 100)
  
  # Factor VIII levels ≤174 IU/dL: HR 3.73
  factor_viii <- rtruncnorm(n_patients, a = 80, b = 300, mean = 165, sd = 35)
  factor_viii_low <- as.numeric(factor_viii <= 174)
  
  # Epinephrine closure time ≥240 seconds: HR 2.74
  epi_closure_time <- rtruncnorm(n_patients, a = 120, b = 400, mean = 220, sd = 45)
  epi_closure_prolonged <- as.numeric(epi_closure_time >= 240)
  
  # UPDATED: Baseline aggregation capacity reflecting CLL and age
  baseline_aggregation <- 95 - (age - 40) * 0.8 - 
                         high_comorbidity_score * 12 - 
                         diabetes * 10 - 
                         cardiovascular_disease * 15 -
                         del17p_tp53 * 8 -
                         (prior_treatments * 5)
  baseline_aggregation <- pmax(baseline_aggregation + rnorm(n_patients, 0, 12), 25)
  baseline_aggregation <- pmin(baseline_aggregation, 110)
  
  return(data.frame(
    platelet_count = platelet_count,
    hypertension = as.logical(hypertension),
    diabetes = as.logical(diabetes),
    cardiovascular_disease = as.logical(cardiovascular_disease),
    ongoing_cvd = as.logical(ongoing_cvd),
    atrial_fibrillation = as.logical(atrial_fibrillation),
    high_comorbidity_score = as.logical(high_comorbidity_score),
    alt = round(alt, 1),
    ast = round(ast, 1),
    bilirubin = round(bilirubin, 2),
    anticoagulants = as.logical(anticoagulants),
    anticoag_type = anticoag_type,
    doac_type = doac_type,
    antiplatelets = as.logical(antiplatelets),
    cyp3a4_inhibitors = as.logical(cyp3a4_inhibitors),
    cyp3a4_inducers = as.logical(cyp3a4_inducers),
    ppi_use = as.logical(ppi_use),
    antihypertensives = as.logical(antihypertensives),
    uric_acid_inhibitors = as.logical(uric_acid_inhibitors),
    del17p_tp53 = as.logical(del17p_tp53),
    unmutated_ighv = as.logical(unmutated_ighv),
    baseline_aggregation = round(baseline_aggregation, 1),
    cll_stage = cll_stage,
    prior_treatments = prior_treatments,
    vwf_activity = round(vwf_activity, 1),
    vwf_low = as.logical(vwf_low),
    factor_viii = round(factor_viii, 1),
    factor_viii_low = as.logical(factor_viii_low),
    epi_closure_time = round(epi_closure_time, 1),
    epi_closure_prolonged = as.logical(epi_closure_prolonged)
  ))
}

# =============================================================================
# PART 3: GENETIC POLYMORPHISMS GENERATOR
# =============================================================================

generate_genetic_variants <- function(demographics) {
  n_patients <- nrow(demographics)
  ethnicity <- demographics$ethnicity
  
  # UPDATED: CYP3A4 variants with ethnicity-specific frequencies
  # Adjusted for CLL population (predominantly Caucasian)
  cyp3a4_1b_freq <- case_when(
    ethnicity == "Caucasian" ~ 0.04,
    ethnicity == "African American" ~ 0.67,
    ethnicity == "Asian" ~ 0.02,
    ethnicity == "Hispanic" ~ 0.28,
    TRUE ~ 0.15
  )
  
  cyp3a4_1b_genotype <- sapply(cyp3a4_1b_freq, function(freq) {
    sample(c("*1/*1", "*1/*1B", "*1B/*1B"), 1, 
           prob = c((1-freq)^2, 2*freq*(1-freq), freq^2))
  })
  
  # UPDATED: CYP3A5 variants 
  cyp3a5_3_freq <- case_when(
    ethnicity == "Caucasian" ~ 0.95,
    ethnicity == "African American" ~ 0.27,
    ethnicity == "Asian" ~ 0.70,
    ethnicity == "Hispanic" ~ 0.70,
    TRUE ~ 0.70
  )
  
  cyp3a5_genotype <- sapply(cyp3a5_3_freq, function(freq) {
    sample(c("*1/*1", "*1/*3", "*3/*3"), 1,
           prob = c((1-freq)^2, 2*freq*(1-freq), freq^2))
  })
  
  # PLCG2 variants (affect downstream signaling and platelet function)
  # Higher prevalence in CLL patients
  plcg2_variants <- rbinom(n_patients, 1, 0.08)  # Increased from 0.05
  
  # BTK C481S mutation (affects ibrutinib binding - resistance mechanism)
  # Can develop during treatment
  btk_c481s_mutation <- rbinom(n_patients, 1, 0.03)  # Slightly higher in CLL
  
  # UPDATED: Additional resistance mutations relevant to CLL
  # PLCG2 mutations (acquired resistance)
  plcg2_resistance_mutations <- rbinom(n_patients, 1, 0.02)
  
  # Calculate metabolizer phenotypes
  cyp3a4_activity <- case_when(
    cyp3a4_1b_genotype == "*1/*1" ~ 1.0,
    cyp3a4_1b_genotype == "*1/*1B" ~ 1.2,
    cyp3a4_1b_genotype == "*1B/*1B" ~ 1.4
  )
  
  cyp3a5_activity <- case_when(
    cyp3a5_genotype == "*1/*1" ~ 1.0,
    cyp3a5_genotype == "*1/*3" ~ 0.5,
    cyp3a5_genotype == "*3/*3" ~ 0.1
  )
  
  # Combined metabolizer status with interaction effects
  metabolizer_score <- cyp3a4_activity + cyp3a5_activity
  metabolizer_phenotype <- case_when(
    metabolizer_score < 0.8 ~ "Poor",
    metabolizer_score < 1.2 ~ "Intermediate", 
    metabolizer_score < 1.8 ~ "Normal",
    TRUE ~ "Ultrarapid"
  )
  
  return(data.frame(
    cyp3a4_genotype = cyp3a4_1b_genotype,
    cyp3a5_genotype = cyp3a5_genotype,
    plcg2_variants = as.logical(plcg2_variants),
    btk_c481s_mutation = as.logical(btk_c481s_mutation),
    plcg2_resistance_mutations = as.logical(plcg2_resistance_mutations),
    cyp3a4_activity = round(cyp3a4_activity, 2),
    cyp3a5_activity = round(cyp3a5_activity, 2),
    metabolizer_phenotype = metabolizer_phenotype
  ))
}

# =============================================================================
# PART 4: TREATMENT PATTERNS GENERATOR
# =============================================================================

generate_treatment_patterns <- function(population) {
  n_patients <- nrow(population)
  
  # UPDATED: Treatment allocation (80% ibrutinib, 20% control)
  treatment_arm <- sample(c("Ibrutinib_420mg", "Control"), 
                         n_patients, replace = TRUE, prob = c(0.8, 0.2))
  
  # UPDATED: Follow-up patterns
  # EVIdeNCE study: 70.2% two-year retention rate
  # Median time to discontinuation: 15-24 months
  
  # Treatment duration modeling (for ibrutinib patients)
  treatment_duration_months <- numeric(n_patients)
  
  # For ibrutinib patients
  ibr_patients <- treatment_arm == "Ibrutinib_420mg"
  if (sum(ibr_patients) > 0) {
    # 70.2% complete 24 months, 29.8% discontinue
    completes_24m <- rbinom(sum(ibr_patients), 1, 0.702)
    
    # For those who complete: extend beyond 24 months
    treatment_duration_months[ibr_patients & completes_24m == 1] <- 
      rtruncnorm(sum(ibr_patients & completes_24m == 1), a = 24, b = 60, mean = 36, sd = 12)
    
    # For those who discontinue: median 15-24 months
    treatment_duration_months[ibr_patients & completes_24m == 0] <- 
      rtruncnorm(sum(ibr_patients & completes_24m == 0), a = 1, b = 24, mean = 18, sd = 8)
  }
  
  # For control patients (observational follow-up)
  control_patients <- treatment_arm == "Control"
  if (sum(control_patients) > 0) {
    treatment_duration_months[control_patients] <- 
      rtruncnorm(sum(control_patients), a = 6, b = 36, mean = 24, sd = 8)
  }
  
  # Convert to days
  followup_days <- round(treatment_duration_months * 30.44)
  
  # Discontinuation reasons (for ibrutinib patients who discontinue)
  discontinuation_reason <- character(n_patients)
  discontinuation_reason[treatment_arm == "Control"] <- "Not_applicable"
  discontinuation_reason[ibr_patients & completes_24m == 1] <- "Ongoing"
  
  # For those who discontinue ibrutinib
  disc_patients <- ibr_patients & completes_24m == 0
  if (sum(disc_patients) > 0) {
    discontinuation_reason[disc_patients] <- 
      sample(c("Adverse_events", "Disease_progression", "Death", "Other"),
             sum(disc_patients), replace = TRUE,
             prob = c(0.49, 0.17, 0.15, 0.19))  # Based on EVIdeNCE study
  }
  
  # Dose modifications (for ibrutinib patients)
  dose_modifications <- logical(n_patients)
  dose_modifications[ibr_patients] <- rbinom(sum(ibr_patients), 1, 0.35)  # 35% experience dose modifications
  
  return(data.frame(
    treatment_arm = treatment_arm,
    treatment_duration_months = round(treatment_duration_months, 1),
    followup_days = followup_days,
    discontinuation_reason = discontinuation_reason,
    dose_modifications = dose_modifications
  ))
}

# =============================================================================
# PART 5: MAIN POPULATION GENERATOR FUNCTION
# =============================================================================

generate_synthetic_population <- function(n_patients = 5000, save_file = TRUE) {
  
  cat("Generating evidence-based synthetic patient population...\n")
  cat("Based on 14,000+ patient meta-analyses and 2024 bleeding risk data\n")
  cat("Number of patients:", n_patients, "\n\n")
  
  # Generate demographics
  cat("1. Generating demographic variables...\n")
  demographics <- generate_demographics(n_patients)
  cat("   Generated demographics reflecting CLL population (median age 71)\n")
  
  # Generate clinical variables
  cat("2. Generating clinical variables...\n")
  clinical <- generate_clinical_variables(demographics)
  cat("   Generated clinical variables with bleeding risk factors\n")
  
  # Generate genetic variants
  cat("3. Generating genetic polymorphisms...\n")
  genetic <- generate_genetic_variants(demographics)
  cat("   Generated genetic variants for CLL population\n")
  
  # Generate treatment patterns
  cat("4. Generating treatment patterns...\n")
  treatment <- generate_treatment_patterns(cbind(demographics, clinical, genetic))
  cat("   Generated treatment patterns with discontinuation modeling\n")
  
  # Combine all data
  cat("5. Combining patient data and finalizing population...\n")
  population <- cbind(demographics, clinical, genetic, treatment)
  
  cat("   Final population: ", n_patients, " patients ready for virtual trial\n")
  
  if (save_file) {
    # Save population to CSV using safe file utilities
    results_dir <- get_ml_results_dir()
    safe_csv_save(population, build_path(results_dir, "synthetic_patient_population.csv"))
    cat("\nSynthetic population saved to 'synthetic_patient_population.csv'\n")
  }
  
  # Print comprehensive summary statistics
  cat("\n=== POPULATION SUMMARY ===\n")
  cat("Age distribution:\n")
  cat("  Under 65:", round(mean(population$age < 65) * 100, 1), "%\n")
  cat("  65-69:", round(mean(population$age >= 65 & population$age < 70) * 100, 1), "%\n") 
  cat("  70+:", round(mean(population$age >= 70) * 100, 1), "%\n")
  cat("  Median age:", median(population$age), "years\n\n")
  
  cat("Sex distribution:\n")
  print(table(population$sex))
  cat("\nEthnicity distribution:\n")
  print(table(population$ethnicity))
  
  cat("\nComorbidity prevalence:\n")
  cat("  Cardiovascular disease:", round(mean(population$cardiovascular_disease) * 100, 1), "%\n")
  cat("  High comorbidity score:", round(mean(population$high_comorbidity_score) * 100, 1), "%\n")
  cat("  Diabetes:", round(mean(population$diabetes) * 100, 1), "%\n")
  cat("  Atrial fibrillation:", round(mean(population$atrial_fibrillation) * 100, 1), "%\n")
  
  cat("\nBleeding risk factors:\n")
  cat("  Anticoagulant use:", round(mean(population$anticoagulants) * 100, 1), "%\n")
  cat("  Antiplatelet use:", round(mean(population$antiplatelets) * 100, 1), "%\n")
  cat("  Low vWF activity:", round(mean(population$vwf_low) * 100, 1), "%\n")
  cat("  Low Factor VIII:", round(mean(population$factor_viii_low) * 100, 1), "%\n")
  cat("  Prolonged epinephrine closure:", round(mean(population$epi_closure_prolonged) * 100, 1), "%\n")
  
  cat("\nCLL disease characteristics:\n")
  cat("  del(17p)/TP53 mutations:", round(mean(population$del17p_tp53) * 100, 1), "%\n")
  cat("  Unmutated IGHV:", round(mean(population$unmutated_ighv) * 100, 1), "%\n")
  
  cat("\nTreatment allocation:\n")
  print(table(population$treatment_arm))
  
  cat("\nMetabolizer phenotype distribution:\n")
  print(table(population$metabolizer_phenotype))
  
  cat("\nTreatment duration (ibrutinib patients):\n")
  ibr_patients <- population$treatment_arm == "Ibrutinib_420mg"
  if (sum(ibr_patients) > 0) {
    cat("  Median duration:", round(median(population$treatment_duration_months[ibr_patients]), 1), "months\n")
    cat("  Two-year retention:", round(mean(population$treatment_duration_months[ibr_patients] >= 24) * 100, 1), "%\n")
  }
  
  return(population)
}

# =============================================================================
# PART 6: ENHANCED VISUALIZATION FUNCTIONS
# =============================================================================

plot_population_characteristics <- function(population) {
  
  # Age distribution with clinical benchmarks
  p1 <- ggplot(population, aes(x = age, fill = sex)) +
    geom_histogram(alpha = 0.7, bins = 20, position = "identity") +
    geom_vline(xintercept = 71, linetype = "dashed", color = "red", size = 1) +
    annotate("text", x = 75, y = max(table(cut(population$age, 20))) * 0.8, 
             label = "Clinical\nmedian: 71", color = "red") +
    labs(title = "CLL Age Distribution", 
         subtitle = "Based on EVIdeNCE study (N=309)",
         x = "Age (years)", y = "Count") +
    theme_minimal()
  
  # Bleeding risk factor distribution
  bleeding_factors <- population %>%
    summarise(
      Anticoagulants = mean(anticoagulants) * 100,
      Antiplatelets = mean(antiplatelets) * 100,
      `Low vWF` = mean(vwf_low) * 100,
      `Low Factor VIII` = mean(factor_viii_low) * 100,
      `Prolonged EPI closure` = mean(epi_closure_prolonged) * 100,
      `Age ≥70` = mean(age >= 70) * 100
    ) %>%
    tidyr::pivot_longer(everything(), names_to = "Risk_Factor", values_to = "Prevalence")
  
  p2 <- ggplot(bleeding_factors, aes(x = reorder(Risk_Factor, Prevalence), y = Prevalence)) +
    geom_col(fill = "steelblue", alpha = 0.8) +
    geom_text(aes(label = paste0(round(Prevalence, 1), "%")), hjust = -0.1) +
    coord_flip() +
    labs(title = "Bleeding Risk Factor Prevalence",
         subtitle = "CLL population characteristics",
         x = "Risk Factor", y = "Prevalence (%)") +
    theme_minimal()
  
  # Treatment duration distribution
  ibr_data <- population %>% filter(treatment_arm == "Ibrutinib_420mg")
  
  p3 <- ggplot(ibr_data, aes(x = treatment_duration_months)) +
    geom_histogram(bins = 20, fill = "darkgreen", alpha = 0.7) +
    geom_vline(xintercept = 24, linetype = "dashed", color = "red", size = 1) +
    annotate("text", x = 30, y = max(table(cut(ibr_data$treatment_duration_months, 20))) * 0.8,
             label = "24-month\nbenchmark", color = "red") +
    labs(title = "Treatment Duration Distribution",
         subtitle = paste0("70.2% complete 24 months (actual: ", 
                          round(mean(ibr_data$treatment_duration_months >= 24) * 100, 1), "%)"),
         x = "Treatment Duration (months)", y = "Count") +
    theme_minimal()
  
  # Anticoagulant type distribution
  anticoag_data <- population %>% 
    filter(anticoagulants) %>%
    count(anticoag_type, doac_type) %>%
    mutate(label = ifelse(anticoag_type == "DOAC", doac_type, anticoag_type))
  
  p4 <- ggplot(anticoag_data, aes(x = "", y = n, fill = label)) +
    geom_bar(stat = "identity", width = 1) +
    coord_polar("y", start = 0) +
    labs(title = "Anticoagulant Type Distribution",
         subtitle = "Among patients using anticoagulants",
         fill = "Anticoagulant Type") +
    theme_void()
  
  # Save plots
  ggsave("age_distribution.png", p1, width = 12, height = 8)
  ggsave("bleeding_risk_factors.png", p2, width = 12, height = 8)
  ggsave("treatment_duration.png", p3, width = 12, height = 8)
  ggsave("anticoagulant_types.png", p4, width = 10, height = 8)
  
  cat("Population characteristic plots saved.\n")
  
  return(list(p1, p2, p3, p4))
}

# =============================================================================
# EXAMPLE USAGE
# =============================================================================

if (FALSE) {  # Set to TRUE to run example
  # Generate population
  population <- generate_synthetic_population(n_patients = 5000)
  
  # Create visualizations
  plots <- plot_population_characteristics(population)
  
  # View first few patients
  head(population)
}

cat("Evidence-based synthetic population generator loaded successfully!\n")
cat("Use generate_synthetic_population(n_patients) to create your safety trial population.\n")
cat("Incorporates 2024 bleeding risk data from 14,000+ patient meta-analyses.\n")